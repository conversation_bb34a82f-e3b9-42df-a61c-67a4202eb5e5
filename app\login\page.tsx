"use client";
 
import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON>ethoscope, User, Lock, Eye, EyeOff } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
// import Logo from "@/components/Logo";
import logoSrc from "@/public/JedAI-logo.png"; // static import
import { AUTH, FORGOT_PASSWORD } from "@/constants/apiRoutes";
import { useDispatch } from "react-redux";
import {
  setAccessToken,
  setClinicId,
  setClinicName,
  setDisplayName,
  setRefreshToken,
  setRoleId,
  setUserId,
  setEmails,
  setFirstName,   // ← import
  setLastName,
} from "@/store/userSlice";
import { Logo } from "@/components/Logo";
import { AppDispatch } from "@/store/store";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { toast } from "@/components/ui/use-toast";
 
export default function LoginPage() {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [userMessage, setUserMessage] = useState("");
  const [suggestedAction, setSuggestedAction] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isForgotLoading, setIsForgotLoading] = useState(false);
  const [isForgotSuccess, setIsForgotSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
 
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUserMessage("");
    setSuggestedAction("");
    setIsLoading(true);
 
    try {
      const res = await fetch(AUTH, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ Username: email, Password: password }),
      });
 
      const payload = await res.json();
      console.log("login payload:", payload);
 
      if (!res.ok) {
        const err = payload.data || {};
        setUserMessage(
          err.userMessage ??
            err.message ??
            payload.errorMessage ??
            "Login failed"
        );
        setSuggestedAction(err.suggestedAction ?? "");
        return;
      }
 
      // --- success path ---
      const accessToken =
        payload.data?.accessToken ?? payload.data?.accesstoken;
      const refreshToken =
        payload.data?.refreshToken ?? payload.data?.refreshtoken;
 
      if (!accessToken || !refreshToken) {
        setUserMessage("Login succeeded but no tokens were returned");
        return;
      }
      const userId = payload.data?.userId;
      console.log("login userId:", userId);
 
      if (userId) {
        dispatch(setUserId(userId));
      } else {
        console.warn("No userId in login response");
      }
      // 1️⃣ store tokens in Redux
      dispatch(setAccessToken(accessToken));
      dispatch(setRefreshToken(refreshToken));
 
      // 2️⃣ store the rest of your user info in Redux as before
      dispatch(setRoleId(payload.data.role_id));
      dispatch(setClinicId(payload.data.clinic_id));
      if (payload.data.clinic_name) {
        dispatch(setClinicName(payload.data.clinic_name));
      }
    // store first + last name
    if (payload.data.firstName) {
      dispatch(setFirstName(payload.data.firstName.trim()));
    }
    if (payload.data.lastName) {
      dispatch(setLastName(payload.data.lastName.trim()));
    }
 
    // derive and store displayName
    const displayName = `${payload.data.firstName.trim()} ${payload.data.lastName.trim()}`;
    dispatch(setDisplayName(displayName));
     
      dispatch(setEmails(email));
      // 4️⃣ finally navigate
    if (payload.data.role_id === 1) {
    router.replace("/usermanagement")
  } else {
    router.replace("/dashboard")
  }
      // router.push("/");
    } catch (err) {
      console.error("Login error:", err);
      setUserMessage("Network error. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };
 
  const handleForgotPassword = async () => {
    // reset prior state
    setUserMessage("");
    setIsForgotSuccess(false);
 
    // 1️⃣ require an email
    if (!email.trim()) {
      setUserMessage("Please enter your email above first.");
      return;
    }
 
    setIsForgotLoading(true);
    try {
      // 2️⃣ use fetchWithRefresh instead of fetch
      const res = await fetchWithRefresh(
        FORGOT_PASSWORD,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email: email.trim() }),
        },
        router
      );
 
      // 3️⃣ guard against null (failed refresh)
      if (!res) {
        setUserMessage("Unable to send reset link. Please try again.");
        return;
      }
 
      const payload = await res.json();
 
      if (res.ok) {
        // 4️⃣ success toast + state
        toast({
          title: "Reset link sent",
          description: "Check your inbox for the password reset link.",
        });
        setUserMessage("Password reset link sent to your inbox.");
        setIsForgotSuccess(true);
      } else {
        // 5️⃣ server-side error path
        const msg = payload.message || "Unable to send reset link.";
        toast({
          variant: "destructive",
          title: "Reset failed",
          description: msg,
        });
        setUserMessage(msg);
      }
    } catch (err: any) {
      // 6️⃣ network error path
      console.error("Forgot password error:", err);
      toast({
        variant: "destructive",
        title: "Network error",
        description: "Could not reach the server. Please try again.",
      });
      setUserMessage("Network error. Please try again.");
    } finally {
      setIsForgotLoading(false);
    }
  };
 
  return (
    <div className="min-h-screen bg-blue-50 flex flex-col items-center py-6 px-4">
      {/* Header */}
      <div className="text-center mt-10">
        <div className="inline-flex items-center justify-center  rounded-full p-4">
          {/* <Stethoscope className="h-6 w-6 text-white" /> */}
          {/* <img className="h-[45px]" alt="JED AI LOGO" src="/JedAI-logo.png" /> */}
          <Logo />
        </div>
        <p className="mt-1 text-gray-600">Advanced Dental Analysis Platform</p>
      </div>
 
      {/* Form Card */}
      <div className="mt-4 w-full max-w-md bg-white rounded-2xl shadow-lg p-6">
        <h2 className="text-2xl flex items-center justify-center font-semibold text-gray-900">
          Sign In
        </h2>
 
        <form onSubmit={handleSubmit} className="mt-4 space-y-5">
          {/* Email */}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-900 mb-1"
            >
              Email
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                id="email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="
    w-full pl-10 pr-3 py-2
    bg-white text-gray-900 border border-gray-200
    rounded-lg
    focus:outline-none focus:ring-2 focus:ring-blue-300
    dark:bg-white dark:text-gray-900 dark:border-gray-200
  "
              />
            </div>
          </div>
 
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-900 mb-1"
            >
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                className="
   w-full pl-10 pr-10 py-2
   bg-white text-gray-900 border border-gray-200
   rounded-lg
   focus:outline-none focus:ring-2 focus:ring-blue-300
   dark:bg-white dark:text-gray-900 dark:border-gray-200
 "
              />
              <button
                type="button"
                onClick={() => setShowPassword((v) => !v)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>
 
          {/* Forgot */}
          <div className="flex justify-end">
            <button
              type="button"
              onClick={handleForgotPassword}
              disabled={isForgotLoading}
              className="text-sm text-blue-600 hover:underline disabled:opacity-50"
            >
              {isForgotLoading ? "Sending..." : "Forgot password?"}
            </button>
          </div>
 
          {/* Submit */}
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full py-2 bg-black text-white hover:bg-gray-800"
          >
            {isLoading ? "Signing In…" : "Sign In"}
          </Button>
        </form>
 
        {/* Error */}
        {/* userMessage + suggestedAction */}
        {userMessage && (
          <div className="mt-4 text-center">
            <p
              className={`text-sm ${
                isForgotSuccess ? "text-green-600" : "text-red-600"
              }`}
            >
              {userMessage}
            </p>
            {suggestedAction && (
              <p className="mt-1 text-sm text-gray-600">{suggestedAction}</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}