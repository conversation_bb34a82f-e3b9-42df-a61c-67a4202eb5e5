"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
} from "recharts";
import {
  Download,
  Users,
  Activity,
  Sparkles,
  Calendar,
  TrendingUp,
  Filter,
  BarChart2,
  CheckCircle,
  TrendingDown,
} from "lucide-react";
import { setISOWeek, startOfISOWeek, format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setCalendarOpen, setSelectedDate } from "@/store/dentalSlice";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { GET_PATIENTS_BY_UUID, GET_PATIENT_FLOW } from "@/constants/apiRoutes";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { RefreshCw } from "lucide-react"; 


export default function DentalPracticeDashboard() {
  const dispatch = useAppDispatch();
  const router = useRouter();
const userId = useAppSelector((s: RootState) => s.user.userId)
console.log("🆔 userId from Redux:", userId)

 // store the fetched clinicName
 
const [clinicNameLocal, setClinicNameLocal] = useState<string>("");
const [clinicLoading, setClinicLoading] = useState<boolean>(false);
const [systemStatus, setSystemStatus] = useState< Record<string, number> | null >(null); const [statusLoading, setStatusLoading] = useState(false);
const [showAllStatus, setShowAllStatus] = useState(false);
  const selectedDate  = useAppSelector((s: RootState) => s.dental.selectedDate)
  const clinicName = useSelector((state: RootState) => state.user.clinicName);
  const {
    dateRange,

    endDate,
    isCalendarOpen,
    activeChart,
    kpiData,
    patientFlowData,
  } = useAppSelector((state) => state.dental);
  
  const [pickedDate, setPickedDate] = useState<Date | null>(null);
  const [patientCount, setPatientCount] = useState<number>(0);
  const [procedureCount, setProcedureCount] = useState<number>(0);
  // after patientCount / procedureCount…
  const [flowInterval, setFlowInterval] = useState<
    "daily" | "weekly" | "monthly"
  >("daily");
  // const [flowData, setFlowData] = useState<{ date: string; count: number }[]>([]);
  const {
    /*…,*/
    /* remove patientFlowData */
  } = useAppSelector((state) => state.dental);
  const [flowData, setFlowData] = useState<{ date: string; count: number }[]>(
    []
  );
  const [newPatientsChange, setNewPatientsChange] = useState<number>(0);
  const [proceduresChange, setProceduresChange] = useState<number>(0);

  // useEffect(() => {
  //   const uuid = Cookies.get("userId");
  //   if (!uuid) return;

  //   fetch(
  //     `${GET_PATIENT_FLOW}userId=${encodeURIComponent(
  //       uuid
  //     )}&interval=${flowInterval}`
  //   )
  //     .then((res) => res.json())
  //     .then((json) => {
  //       if (!json.isError) setFlowData(json.data);
  //       else console.error("Flow API error:", json.errorMessage);
  //     })
  //     .catch((err) => console.error("Flow fetch failed:", err));
  // }, [flowInterval]); // ← now it re‐runs whenever you change the dropdown

  // ② whenever selectedDate (YYYY-MM-DD) changes, re-fetch
  
  // ─── Fetch System Status ─────────────────────────────────────────────────────────
  
    const fetchStatus = async () => {
      setStatusLoading(true);
      try {
        const res = await fetchWithRefresh(
          "https://jedaiportal-************.us-east1.run.app/api/DashBoard/system-status",
          { method: "GET", credentials: "include" },
          router
        );
        if (!res) return; // already redirected on auth-failure
        const json = await res.json();
        if (json && typeof json === "object" && json.health && typeof json.health === "object") {
      setSystemStatus(json.health);
    } else {
      console.warn("Unexpected status payload or no health data:", json);
      setSystemStatus({}); // Set to empty object to avoid undefined errors
    }
      } catch (err) {
        console.error("Failed to load system status:", err);
      } finally {
        setStatusLoading(false);
      }
    };
  
  useEffect(() => {
  

    fetchStatus();
  }, [router]);


  useEffect(() => {
  console.log("🏃‍♂️ fetchFlow effect", { userId, flowInterval });
  if (!userId) return;

  const fetchFlow = async () => {
    const url = `${GET_PATIENT_FLOW}userId=${encodeURIComponent(userId)}&interval=${flowInterval}`;
    console.log("➡️ hitting", url);
    const res = await fetchWithRefresh(url, {}, router);
    if (!res) return;
    const json = await res.json();
    console.log("🔄 flow JSON:", json);
    if (!json.isError) setFlowData(json.data);
  };

  fetchFlow();
}, [userId, flowInterval, router]);

  useEffect(() => {
  if (!userId) return;

  const fetchUserDetails = async () => {
    setClinicLoading(true);
    try {
      const res = await fetchWithRefresh(
        `https://jedaiportal-************.us-east1.run.app/api/UserManagement/getuserid?userId=${encodeURIComponent(userId)}`,
        { method: "GET", credentials: "include" },
        router
      );
      if (!res) return;
      const json = await res.json();
      // Check if json.data and json.data.clinicName exist before setting
    if (!json.isError && json.data && typeof json.data === "object" && json.data.clinicName) {
      setClinicNameLocal(json.data.clinicName);
    } else {
      console.warn("Failed to load clinicName or no clinicName data:", json.errorMessage || json);
      setClinicNameLocal(""); // Set to empty string to avoid undefined errors
    }
    } catch (err) {
      console.error("Error fetching user details:", err);
    } finally {
      setClinicLoading(false);
    }
  };

  fetchUserDetails();
}, [userId, router]);

  
  // useEffect(() => {
  //   const uuid = Cookies.get("userId");
  //   if (!uuid || !selectedDate) return;
  //   const url = `${GET_PATIENTS_BY_UUID}uuid=${encodeURIComponent(
  //     uuid
  //   )}&date=${encodeURIComponent(selectedDate)}`;

  //   fetch(url)
  //     .then((res) => res.json())
  //     .then((json) => {
  //       if (!json.isError) {
  //         const {
  //           patientCount,
  //           procedureCount,
  //           patientCountPercentChange,
  //           procedureCountPercentChange,
  //         } = json.data;

  //         setPatientCount(patientCount);
  //         setProcedureCount(procedureCount);

  //         setNewPatientsChange(patientCountPercentChange);
  //         setProceduresChange(procedureCountPercentChange);
  //       } else {
  //         console.error("API error:", json.errorMessage);
  //       }
  //     })
  //     .catch((err) => console.error("Fetch failed:", err));
  // }, [selectedDate]);

useEffect(() => {
  console.log("🏃‍♂️ fetchMetrics effect", { userId, selectedDate });
  if (!userId || !selectedDate) return;

  const fetchMetrics = async () => {
    const url = `${GET_PATIENTS_BY_UUID}uuid=${encodeURIComponent(userId)}&date=${encodeURIComponent(selectedDate)}`;
    console.log("➡️ hitting", url);
    const res = await fetchWithRefresh(url, {}, router);
    if (!res) return;
    const json = await res.json();
    console.log("🔄 metrics JSON:", json);
    if (!json.isError) {
      setPatientCount(json.data.patientCount);
      setProcedureCount(json.data.procedureCount);
      setNewPatientsChange(json.data.patientCountPercentChange);
      setProceduresChange(json.data.procedureCountPercentChange);
    }
  };

  fetchMetrics();
}, [userId, selectedDate, router]);



  useEffect(() => {
    if (pickedDate) console.log("Picked date:", pickedDate);
  }, [pickedDate]);

  const handleExportReport = () => {
    alert("Exporting report...");
  };

  const handleNewPatientsClick = () => {
    // Check if selectedDate exists, and include it as a query parameter
  // const query = selectedDate ? `?date=${encodeURIComponent(selectedDate)}` : '';
  // router.push(`/patients${query}`);
    router.push("/patients");
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return format(date, "MMM d, yyyy");
  };

  const getDateRangeText = () => {
    if (!selectedDate) return "May 30, 2025";
    return formatDate(selectedDate);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="px-6 py-3 bg-card border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
           <p className="text-sm text-muted-foreground">
  {clinicLoading
    ? "Loading clinic…"
    : clinicNameLocal || "Couture Dental Clinic"}
</p>

          </div>
          <div className="flex items-center gap-3">
            <Popover
              open={isCalendarOpen}
              onOpenChange={(open) => dispatch(setCalendarOpen(open))}
            >
              <PopoverTrigger asChild>
                <Button variant="outline" className="gap-2 min-h-[44px] px-4">
                  <Calendar size={16} />
                  <span>{getDateRangeText()}</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <CalendarComponent
                  mode="single"
                  selected={selectedDate ? new Date(selectedDate) : new Date()}
                  onSelect={(date) => {
                    // ignore the "unselect" click
                    if (!date) return;

                    // format YYYY-MM-DD in local time
                    const ymd = format(date, "yyyy-MM-dd");
                    dispatch(setSelectedDate(ymd));
                    dispatch(setCalendarOpen(false));
                  }}
                  className="rounded border"
                />
              </PopoverContent>
            </Popover>

          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="p-6">
        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* New Patients Card - Clickable */}
          <Card
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={handleNewPatientsClick}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Users className="text-primary" size={24} />
                </div>
                <Badge
                  className={`${
                    newPatientsChange >= 0
                      ? "bg-green-500 hover:bg-green-500"
                      : "bg-red-500 hover:bg-red-500"
                  } flex items-center`}
                >
                  {newPatientsChange >= 0 ? (
                    <TrendingUp size={14} className="mr-1" />
                  ) : (
                    <TrendingDown size={14} className="mr-1" />
                  )}
                  {Math.abs(newPatientsChange)}%
                </Badge>
              </div>
              <h3 className="text-3xl font-bold mb-1">{patientCount}</h3>
              <p className="text-sm text-muted-foreground">New Patients</p>
            </CardContent>
          </Card>

          {/* Procedures Performed */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-accent flex items-center justify-center">
                  <Activity className="text-accent-foreground" size={24} />
                </div>
                <Badge
                  className={`${
                    proceduresChange >= 0
                      ? "bg-green-500 hover:bg-green-500"
                      : "bg-red-500 hover:bg-red-500"
                  } flex items-center`}
                >
                  {proceduresChange >= 0 ? (
                    <TrendingUp size={14} className="mr-1" />
                  ) : (
                    <TrendingDown size={14} className="mr-1" />
                  )}
                  {Math.abs(proceduresChange)}%
                </Badge>
              </div>
              <h3 className="text-3xl font-bold mb-1">{procedureCount}</h3>
              <p className="text-sm text-gray-500">Procedures Performed</p>
            </CardContent>
          </Card>

          {/* AI-Assisted Diagnoses */}
          {/* <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-violet-100 flex items-center justify-center">
                  <Sparkles className="text-violet-600" size={24} />
                </div>ff
                <Badge className="bg-green-500 hover:bg-green-500">
                  <TrendingUp size={14} className="mr-1" />
                  {kpiData.aiDiagnoses.change}%
                </Badge>
              </div>
              <h3 className="text-3xl font-bold mb-1">{kpiData.aiDiagnoses.value}</h3>
              <p className="text-sm text-gray-500">AI-Assisted Diagnoses</p>
            </CardContent>
          </Card> */}

          {/* System Status */}
          {/* <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <Activity className="text-green-600" size={24} />
                </div>
              </div>
             
               <h3 className="text-lg font-semibold mb-2">System Status</h3>
              {statusLoading && <p className="text-sm text-gray-500">Loading status…</p>}
              {!statusLoading && systemStatus && (
               <>
              <ul className="space-y-1">
                {(showAllStatus
                  ? Object.entries(systemStatus)
                  : [Object.entries(systemStatus)[0]]
                ).map(([service, code]) => {
                  
                    const label = service
                      .replace(/_/g, " ")
                      .replace(/\b\w/g, (c) => c.toUpperCase());
                    const isUp = code === 200;
                    return (
                      <li key={service} className="flex items-center gap-2">
                        {isUp ? (
                          <CheckCircle className="text-green-500" size={18} />
                        ) : (
                          <TrendingDown className="text-red-500" size={18} />
                        )}
                        <span className="flex-1 text-sm">{label}</span>
                        <Badge
                          className={`${
                            isUp
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          } px-2 py-0.5 text-xs`}
                        >
                          {isUp ? "OK" : `Error ${code}`}
                        </Badge>
                      </li>
                    );
                  })}
                      </ul>

        
              {Object.keys(systemStatus).length > 1 && (
                <button
                  className="mt-2 text-sm text-blue-600 hover:underline"
                  onClick={() => setShowAllStatus((v) => !v)}
                >
                  {showAllStatus ? "Show Less" : "More…"}
                </button>
              )}
            </>)
              }
            </CardContent>
          </Card> */}
<Card>
  <CardContent className="p-6">
    {/* Header: status icon + refresh */}
    <div className="flex items-center justify-between mb-4">
      <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
        <Activity className="text-green-600 dark:text-green-400" size={24} />
      </div>
      <button
        onClick={fetchStatus}
        className="p-2 rounded hover:bg-accent"
        aria-label="Refresh status"
      >
        <RefreshCw className="text-muted-foreground hover:text-foreground" size={20} />
      </button>
    </div>

    <h3 className="text-lg font-semibold mb-2">System Status</h3>
    {statusLoading && <p className="text-sm text-gray-500">Loading status…</p>}

    {!statusLoading && systemStatus && (
      <>
        {/* Single summary badge */}
        {(() => {
          const allUp = Object.values(systemStatus).every((code) => code === 200);
          return (
            <Badge
              className={`${
                allUp
                  ? "bg-green-100/20 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                  : "bg-red-100/20 text-red-800 dark:bg-red-900/20 dark:text-red-400"
              } px-3 py-1 text-sm`}
            >
              {allUp ? "Online" : "Offline"}
            </Badge>
          );
        })()}
      </>
    )}
  </CardContent>
</Card>
        </div>

        {/* Patient Flow Section */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <BarChart2 size={20} className="text-muted-foreground" />
              <p className="font-medium text-muted-foreground">Patient Flow</p>
            </div>
            <div className="flex items-center gap-2">
              <Select
                value={flowInterval}
                onValueChange={(val) =>
                  setFlowInterval(val as "daily" | "weekly" | "monthly")
                }
              >
                <SelectTrigger className="w-[120px] h-10">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>

              {/* <Button variant="outline" size="icon" className="h-10 w-10">
                <Download size={16} />
              </Button> */}
            </div>
          </div>

          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-xl">Patient Flow Trends</CardTitle>
              <CardDescription>Daily patient visits over time</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={flowData}
                    margin={{ top: 20, right: 20, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      vertical={false}
                      stroke="hsl(var(--border))"
                    />
                    <XAxis
                      dataKey="date"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "hsl(var(--muted-foreground))" }}
                      // match your original UI date format:
                      tickFormatter={(str) => {
                        if (
                          flowInterval === "weekly" &&
                          typeof str === "string" &&
                          str.includes("-W")
                        ) {
                          const [year, week] = str.split("-W").map(Number);
                          // get the Monday of that ISO week:
                          const isoDate = startOfISOWeek(
                            setISOWeek(new Date(year, 0, 1), week)
                          );
                          return format(isoDate, "MMM d");
                        }
                        // fallback for daily/monthly:
                        const d = new Date(str);
                        return isNaN(d.getTime()) ? str : format(d, "MMM d");
                      }}
                      interval="preserveStartEnd"
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "hsl(var(--muted-foreground))" }}
                      // dynamic max so small numbers still fill the chart:
                      domain={[0, "dataMax"]}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "hsl(var(--popover))",
                        border: "1px solid hsl(var(--border))",
                        borderRadius: "8px",
                        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke="hsl(var(--primary))"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 4, fill: "hsl(var(--primary))" }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
